"""
订单管理系统
"""

import uuid
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional


class OrderType(Enum):
    """订单类型"""

    MARKET = 'market'  # 市价单
    LIMIT = 'limit'  # 限价单


class OrderStyle:
    """订单样式基类"""

    def __init__(self):
        pass

    def get_order_type(self) -> OrderType:
        """获取订单类型"""
        raise NotImplementedError('子类必须实现此方法')

    def get_price(self) -> Optional[float]:
        """获取订单价格"""
        return None

    def __str__(self):
        return f'{self.__class__.__name__}(price={self.get_price()})'


class MarketOrderStyle(OrderStyle):
    """市价单样式"""

    def __init__(self):
        super().__init__()

    def get_order_type(self) -> OrderType:
        return OrderType.MARKET

    def get_price(self) -> Optional[float]:
        return None


class LimitOrderStyle(OrderStyle):
    """限价单样式"""

    def __init__(self, limit_price: float):
        super().__init__()
        if limit_price <= 0:
            raise ValueError('限价单价格必须大于0')
        self.limit_price = limit_price

    def get_order_type(self) -> OrderType:
        return OrderType.LIMIT

    def get_price(self) -> Optional[float]:
        return self.limit_price


class OrderSide(Enum):
    """买卖方向"""

    BUY = 'buy'  # 买入
    SELL = 'sell'  # 卖出


class PositionSide(Enum):
    """多空方向"""

    LONG = 'long'  # 做多/开多
    SHORT = 'short'  # 做空/开空


class OrderStatus(Enum):
    """订单状态"""

    PENDING = 'pending'  # 待处理
    FILLED = 'filled'  # 已成交
    CANCELLED = 'cancelled'  # 已取消
    REJECTED = 'rejected'  # 已拒绝


@dataclass
class Order:
    """订单类"""

    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    volume: int
    price: Optional[float] = None
    status: OrderStatus = OrderStatus.PENDING
    created_time: Optional[datetime] = None
    filled_time: Optional[datetime] = None
    filled_price: Optional[float] = None
    filled_volume: int = 0
    commission: float = 0.0

    # 新增字段：冻结相关
    frozen_cash: float = 0.0  # 冻结的资金（买单）
    frozen_volume: int = 0  # 冻结的股票数量（卖单）

    # 新增字段：预计算成交时间（性能优化）
    expected_fill_time: Optional[datetime] = None  # 预计成交时间

    def __post_init__(self):
        if self.created_time is None:
            self.created_time = datetime.now()


class OrderManager:
    """订单管理器"""

    def __init__(self):
        """
        初始化订单管理器
        """
        self.orders: Dict[str, Order] = {}
        self.trade_records: List[Dict] = []

    def create_order(
        self,
        symbol: str,
        side: OrderSide,
        volume: int,
        order_type: OrderType = OrderType.MARKET,
        price: Optional[float] = None,
    ) -> str:
        """
        创建订单

        Args:
            symbol: 股票代码
            side: 买卖方向
            volume: 数量
            order_type: 订单类型
            price: 价格（限价单需要）

        Returns:
            str: 订单ID
        """
        order_id = str(uuid.uuid4())

        order = Order(
            order_id=order_id,
            symbol=symbol,
            side=side,
            order_type=order_type,
            volume=volume,
            price=price,
        )

        self.orders[order_id] = order
        return order_id

    def fill_order(
        self,
        order_id: str,
        fill_price: float,
        fill_volume: Optional[int] = None,
        commission: Optional[float] = None,
        fill_time: Optional[datetime] = None,
    ) -> bool:
        """
        成交订单

        Args:
            order_id: 订单ID
            fill_price: 成交价格
            fill_volume: 成交数量，如果为None则全部成交
            commission: 已计算的交易成本，如果为None则设为0
            fill_time: 成交时间，如果为None则使用当前时间

        Returns:
            bool: 是否成功
        """
        if order_id not in self.orders:
            return False

        order = self.orders[order_id]
        if order.status != OrderStatus.PENDING:
            return False

        if fill_volume is None:
            fill_volume = order.volume

        # 使用传入的commission，避免重复计算
        if commission is None:
            commission = 0.0

        # 使用传入的成交时间，如果没有则使用当前时间
        actual_fill_time = fill_time if fill_time is not None else datetime.now()

        # 更新订单状态
        order.status = OrderStatus.FILLED
        order.filled_time = actual_fill_time
        order.filled_price = fill_price
        order.filled_volume = fill_volume
        order.commission = commission

        # 记录交易
        trade_record = {
            'order_id': order_id,
            'symbol': order.symbol,
            'side': order.side.value,
            'volume': fill_volume,
            'price': fill_price,
            'commission': commission,
            'timestamp': actual_fill_time,
        }
        self.trade_records.append(trade_record)

        return True

    def cancel_order(self, order_id: str) -> bool:
        """
        取消订单

        Args:
            order_id: 订单ID

        Returns:
            bool: 是否成功
        """
        if order_id not in self.orders:
            return False

        order = self.orders[order_id]
        if order.status != OrderStatus.PENDING:
            return False

        order.status = OrderStatus.CANCELLED
        return True

    def get_pending_orders(
        self, symbol: Optional[str] = None, order_type: Optional[OrderType] = None
    ) -> List[Order]:
        """
        获取待处理订单

        Args:
            symbol: 股票代码，如果为None则返回所有
            order_type: 订单类型，如果为None则返回所有类型

        Returns:
            List[Order]: 待处理订单列表
        """
        pending_orders = [
            order for order in self.orders.values() if order.status == OrderStatus.PENDING
        ]

        if symbol:
            pending_orders = [order for order in pending_orders if order.symbol == symbol]

        if order_type:
            pending_orders = [order for order in pending_orders if order.order_type == order_type]

        return pending_orders

    def get_trade_records(self) -> List[Dict]:
        """获取交易记录"""
        return self.trade_records.copy()

    def clear_all_orders(self):
        """
        清空所有订单记录（每日收盘时调用）

        注意：这会清空所有订单，包括已成交和已取消的订单
        交易记录(trade_records)不会被清空，因为需要保留用于统计
        """
        self.orders.clear()
