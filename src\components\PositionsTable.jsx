function PositionsTable({ positions }) {
  if (!positions || positions.length === 0) {
    return (
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h2 className="card-title">最终持仓明细</h2>
          <div className="text-center py-8">
            <p className="text-gray-500">无持仓记录</p>
          </div>
        </div>
      </div>
    )
  }

  const formatCurrency = (value) => {
    return `¥${value.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
  }

  const formatNumber = (value) => {
    return value.toLocaleString('zh-CN')
  }

  // 计算总市值
  const totalMarketValue = positions.reduce((sum, pos) => sum + (pos.market_value || 0), 0)

  return (
    <div className="card bg-base-100 shadow-xl">
      <div className="card-body">
        <div className="flex justify-between items-center mb-4">
          <h2 className="card-title">最终持仓明细</h2>
          <div className="text-sm">
            <span className="text-gray-500">总市值: </span>
            <span className="font-semibold text-primary">{formatCurrency(totalMarketValue)}</span>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="table table-zebra w-full">
            <thead>
              <tr>
                <th>股票代码</th>
                <th>总持仓</th>
                <th>可用持仓</th>
                <th>平均成本</th>
                <th>市值</th>
                <th>占比</th>
              </tr>
            </thead>
            <tbody>
              {positions.map((position, index) => {
                const percentage = totalMarketValue > 0 ? (position.market_value / totalMarketValue * 100) : 0
                
                return (
                  <tr key={index}>
                    <td className="font-mono font-semibold">{position.symbol}</td>
                    <td className="font-mono">{formatNumber(position.volume)}</td>
                    <td className="font-mono">{formatNumber(position.available_volume)}</td>
                    <td className="font-mono">{formatCurrency(position.avg_cost)}</td>
                    <td className="font-mono font-semibold">{formatCurrency(position.market_value)}</td>
                    <td className="font-mono">
                      <div className="flex items-center space-x-2">
                        <span>{percentage.toFixed(2)}%</span>
                        <progress 
                          className="progress progress-primary w-16" 
                          value={percentage} 
                          max="100"
                        ></progress>
                      </div>
                    </td>
                  </tr>
                )
              })}
            </tbody>
            <tfoot>
              <tr className="font-semibold">
                <td>合计</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td className="font-mono">{formatCurrency(totalMarketValue)}</td>
                <td>100.00%</td>
              </tr>
            </tfoot>
          </table>
        </div>

        {/* 持仓统计 */}
        <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="stat bg-base-200 rounded-lg">
            <div className="stat-title">持仓品种数</div>
            <div className="stat-value text-2xl">{positions.length}</div>
          </div>
          
          <div className="stat bg-base-200 rounded-lg">
            <div className="stat-title">总市值</div>
            <div className="stat-value text-xl text-primary">{formatCurrency(totalMarketValue)}</div>
          </div>
          
          <div className="stat bg-base-200 rounded-lg">
            <div className="stat-title">平均持仓市值</div>
            <div className="stat-value text-xl">
              {positions.length > 0 ? formatCurrency(totalMarketValue / positions.length) : '¥0.00'}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PositionsTable
