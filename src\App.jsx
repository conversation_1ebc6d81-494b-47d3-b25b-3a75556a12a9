import { useState, useEffect } from 'react'
import { Wifi, WifiOff } from 'lucide-react'
import { connectionManager } from './utils/api'
import BacktestDashboard from './components/BacktestDashboard'
import '@mantine/core/styles.css'
import '@mantine/dates/styles.css'
import { MantineProvider } from '@mantine/core'

function App() {
  const [connected, setConnected] = useState(false)

  useEffect(() => {
    // 设置连接状态监听器
    const handleConnectionChange = (isConnected) => {
      console.log(isConnected ? '已连接到服务器' : '与服务器断开连接')
      setConnected(isConnected)
    }

    // 添加连接状态监听器
    connectionManager.addConnectionListener(handleConnectionChange)

    // 开始连接检查
    connectionManager.startConnectionCheck()

    return () => {
      // 清理连接监听器和检查
      connectionManager.removeConnectionListener(handleConnectionChange)
      connectionManager.stopConnectionCheck()
    }
  }, [])

  return (
    <MantineProvider>
      <div className="min-h-screen bg-base-200">
        <div className="navbar bg-base-100 shadow-lg">
          <div className="flex-1">
            <a className="btn btn-ghost text-xl">QuantBack 量化回测系统</a>
          </div>
          <div className="flex-none">
            <div
              className={`badge ${
                connected ? 'badge-success' : 'badge-error'
              } flex items-center gap-1`}
            >
              {connected ? <Wifi size={16} /> : <WifiOff size={16} />}
              {connected ? '已连接' : '未连接'}
            </div>
          </div>
        </div>

        <div className="container mx-auto p-4">
          {connected ? (
            <BacktestDashboard />
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <span className="loading loading-spinner loading-lg"></span>
                <p className="mt-4 text-lg">正在连接服务器...</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </MantineProvider>
  )
}

export default App
