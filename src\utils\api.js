import axios from 'axios'

// API基础配置
const API_BASE_URL = 'http://localhost:8000'
const API_VERSION = '/api/v1'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30秒超时
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API请求: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('API请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    console.log(`API响应: ${response.status} ${response.config.url}`)
    return response
  },
  (error) => {
    console.error('API响应错误:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// API服务类
class BacktestApiService {
  /**
   * 健康检查
   */
  async healthCheck() {
    try {
      const response = await apiClient.get('/health')
      return response.data
    } catch (error) {
      throw new Error(`健康检查失败: ${error.message}`)
    }
  }

  /**
   * 获取策略列表
   */
  async getStrategies() {
    try {
      const response = await apiClient.get(`${API_VERSION}/strategies`)
      return response.data.strategies
    } catch (error) {
      throw new Error(`获取策略列表失败: ${error.response?.data?.detail || error.message}`)
    }
  }

  /**
   * 开始回测
   * @param {Object} params - 回测参数
   * @param {string} params.strategy - 策略名称
   * @param {string} params.start_date - 开始日期 (YYYY-MM-DD)
   * @param {string} params.end_date - 结束日期 (YYYY-MM-DD)
   */
  async startBacktest(params) {
    try {
      const response = await apiClient.post(`${API_VERSION}/backtest/start`, params)
      return response.data
    } catch (error) {
      if (error.response?.status === 409) {
        throw new Error('已有回测任务在运行中')
      } else if (error.response?.status === 404) {
        throw new Error(`未找到策略文件: ${params.strategy}`)
      } else {
        throw new Error(`启动回测失败: ${error.response?.data?.detail || error.message}`)
      }
    }
  }

  /**
   * 获取回测状态
   */
  async getBacktestStatus() {
    try {
      const response = await apiClient.get(`${API_VERSION}/backtest/status`)
      return response.data
    } catch (error) {
      throw new Error(`获取回测状态失败: ${error.response?.data?.detail || error.message}`)
    }
  }

  /**
   * 停止回测
   */
  async stopBacktest() {
    try {
      const response = await apiClient.post(`${API_VERSION}/backtest/stop`)
      return response.data
    } catch (error) {
      if (error.response?.status === 400) {
        throw new Error('当前没有运行中的回测任务')
      } else {
        throw new Error(`停止回测失败: ${error.response?.data?.detail || error.message}`)
      }
    }
  }
}

// 轮询管理器
class PollingManager {
  constructor() {
    this.intervals = new Map()
    this.defaultIntervals = {
      idle: 10000,      // 空闲时10秒
      running: 2000,    // 运行时2秒
      error: 5000,      // 错误时5秒
      completed: 10000  // 完成时10秒
    }
  }

  /**
   * 开始轮询
   * @param {string} key - 轮询标识
   * @param {Function} callback - 轮询回调函数
   * @param {number} interval - 轮询间隔（毫秒）
   */
  startPolling(key, callback, interval = 5000) {
    this.stopPolling(key) // 先停止已存在的轮询
    
    const intervalId = setInterval(async () => {
      try {
        await callback()
      } catch (error) {
        console.error(`轮询错误 [${key}]:`, error)
      }
    }, interval)
    
    this.intervals.set(key, intervalId)
    console.log(`开始轮询 [${key}], 间隔: ${interval}ms`)
  }

  /**
   * 停止轮询
   * @param {string} key - 轮询标识
   */
  stopPolling(key) {
    const intervalId = this.intervals.get(key)
    if (intervalId) {
      clearInterval(intervalId)
      this.intervals.delete(key)
      console.log(`停止轮询 [${key}]`)
    }
  }

  /**
   * 停止所有轮询
   */
  stopAllPolling() {
    for (const [key] of this.intervals) {
      this.stopPolling(key)
    }
  }

  /**
   * 根据状态调整轮询间隔
   * @param {string} key - 轮询标识
   * @param {Function} callback - 轮询回调函数
   * @param {string} status - 当前状态
   */
  adjustPollingInterval(key, callback, status) {
    const interval = this.defaultIntervals[status] || this.defaultIntervals.idle
    this.startPolling(key, callback, interval)
  }
}

// 连接状态管理器
class ConnectionManager {
  constructor(apiService) {
    this.apiService = apiService
    this.isConnected = false
    this.connectionListeners = new Set()
    this.checkInterval = null
  }

  /**
   * 添加连接状态监听器
   * @param {Function} listener - 监听器函数
   */
  addConnectionListener(listener) {
    this.connectionListeners.add(listener)
  }

  /**
   * 移除连接状态监听器
   * @param {Function} listener - 监听器函数
   */
  removeConnectionListener(listener) {
    this.connectionListeners.delete(listener)
  }

  /**
   * 通知连接状态变化
   * @param {boolean} connected - 连接状态
   */
  notifyConnectionChange(connected) {
    if (this.isConnected !== connected) {
      this.isConnected = connected
      this.connectionListeners.forEach(listener => {
        try {
          listener(connected)
        } catch (error) {
          console.error('连接状态监听器错误:', error)
        }
      })
    }
  }

  /**
   * 开始连接检查
   */
  startConnectionCheck() {
    this.stopConnectionCheck()
    
    // 立即检查一次
    this.checkConnection()
    
    // 定期检查连接状态
    this.checkInterval = setInterval(() => {
      this.checkConnection()
    }, 5000) // 每5秒检查一次
  }

  /**
   * 停止连接检查
   */
  stopConnectionCheck() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }
  }

  /**
   * 检查连接状态
   */
  async checkConnection() {
    try {
      await this.apiService.healthCheck()
      this.notifyConnectionChange(true)
    } catch (error) {
      this.notifyConnectionChange(false)
    }
  }
}

// 创建服务实例
const apiService = new BacktestApiService()
const pollingManager = new PollingManager()
const connectionManager = new ConnectionManager(apiService)

// 导出服务
export {
  apiService,
  pollingManager,
  connectionManager,
  BacktestApiService,
  PollingManager,
  ConnectionManager
}

// 默认导出主要服务
export default apiService
