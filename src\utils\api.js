import axios from 'axios'

// API基础配置
const API_BASE_URL = 'http://localhost:8000'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30秒超时
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API请求: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('API请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    console.log(`API响应: ${response.status} ${response.config.url}`)
    return response
  },
  (error) => {
    console.error('API响应错误:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// API服务类
class BacktestApiService {
  /**
   * 获取策略列表
   */
  async getStrategies() {
    try {
      const response = await apiClient.get('/strategies')
      return response.data.strategies
    } catch (error) {
      throw new Error(
        `获取策略列表失败: ${error.response?.data?.detail || error.message}`
      )
    }
  }

  /**
   * 开始回测
   * @param {Object} params - 回测参数
   * @param {string} params.strategy - 策略名称
   * @param {string} params.start_date - 开始日期 (YYYY-MM-DD)
   * @param {string} params.end_date - 结束日期 (YYYY-MM-DD)
   */
  async startBacktest(params) {
    try {
      const response = await apiClient.post('/backtest/start', params)
      return response.data
    } catch (error) {
      if (error.response?.status === 409) {
        throw new Error('已有回测任务在运行中')
      } else if (error.response?.status === 404) {
        throw new Error(`未找到策略文件: ${params.strategy}`)
      } else {
        throw new Error(
          `启动回测失败: ${error.response?.data?.detail || error.message}`
        )
      }
    }
  }
}

// WebSocket管理器
class WebSocketManager {
  constructor() {
    this.ws = null
    this.isConnected = false
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 3000 // 3秒
    this.listeners = new Map()
    this.connectionListeners = new Set()
  }

  /**
   * 连接WebSocket
   */
  connect() {
    const wsUrl = 'ws://localhost:8000/ws'

    try {
      this.ws = new WebSocket(wsUrl)

      this.ws.onopen = () => {
        console.log('WebSocket连接已建立')
        this.isConnected = true
        this.reconnectAttempts = 0
        this.notifyConnectionChange(true)

        // 发送心跳包
        this.startHeartbeat()
      }

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          this.handleMessage(data)
        } catch (error) {
          console.error('解析WebSocket消息失败:', error)
        }
      }

      this.ws.onclose = () => {
        console.log('WebSocket连接已关闭')
        this.isConnected = false
        this.notifyConnectionChange(false)
        this.stopHeartbeat()

        // 自动重连
        this.attemptReconnect()
      }

      this.ws.onerror = (error) => {
        console.error('WebSocket错误:', error)
        this.isConnected = false
        this.notifyConnectionChange(false)
      }
    } catch (error) {
      console.error('创建WebSocket连接失败:', error)
      this.attemptReconnect()
    }
  }

  /**
   * 断开WebSocket连接
   */
  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    this.stopHeartbeat()
    this.isConnected = false
    this.notifyConnectionChange(false)
  }

  /**
   * 处理接收到的消息
   */
  handleMessage(data) {
    const { type } = data
    const listeners = this.listeners.get(type) || []

    listeners.forEach((listener) => {
      try {
        listener(data)
      } catch (error) {
        console.error(`WebSocket消息处理错误 [${type}]:`, error)
      }
    })
  }

  /**
   * 添加消息监听器
   */
  addListener(type, listener) {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, [])
    }
    this.listeners.get(type).push(listener)
  }

  /**
   * 移除消息监听器
   */
  removeListener(type, listener) {
    const listeners = this.listeners.get(type)
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * 添加连接状态监听器
   */
  addConnectionListener(listener) {
    this.connectionListeners.add(listener)
  }

  /**
   * 移除连接状态监听器
   */
  removeConnectionListener(listener) {
    this.connectionListeners.delete(listener)
  }

  /**
   * 通知连接状态变化
   */
  notifyConnectionChange(connected) {
    this.connectionListeners.forEach((listener) => {
      try {
        listener(connected)
      } catch (error) {
        console.error('连接状态监听器错误:', error)
      }
    })
  }

  /**
   * 尝试重连
   */
  attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('WebSocket重连次数已达上限，停止重连')
      return
    }

    this.reconnectAttempts++
    console.log(
      `WebSocket重连尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts}`
    )

    setTimeout(() => {
      this.connect()
    }, this.reconnectInterval)
  }

  /**
   * 开始心跳检测
   */
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send('ping')
      }
    }, 30000) // 30秒心跳
  }

  /**
   * 停止心跳检测
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }
}

// 创建服务实例
const apiService = new BacktestApiService()
const wsManager = new WebSocketManager()

// 导出服务
export { apiService, wsManager, BacktestApiService, WebSocketManager }

// 默认导出主要服务
export default apiService
