import { useState, useEffect } from 'react'
import { apiService, pollingManager } from '../utils/api'
import BacktestConfigRow from './BacktestConfigRow'
import BacktestStatus from './BacktestStatus'
import ResultsDisplay from './ResultsDisplay'

function BacktestDashboard() {
  const [strategies, setStrategies] = useState([])
  const [selectedStrategy, setSelectedStrategy] = useState('')
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const [backtestStatus, setBacktestStatus] = useState({
    is_running: false,
    current_strategy: null,
    start_date: null,
    end_date: null,
    progress: 0,
    status: 'idle',
    error_message: null,
    results: null,
  })
  const [results, setResults] = useState(null)
  const [error, setError] = useState('')

  useEffect(() => {
    // 初始化：获取策略列表
    loadStrategies()

    // 开始状态轮询
    startStatusPolling()

    return () => {
      // 清理轮询
      pollingManager.stopAllPolling()
    }
  }, [])

  // 加载策略列表
  const loadStrategies = async () => {
    try {
      const strategiesList = await apiService.getStrategies()
      setStrategies(strategiesList || [])
    } catch (error) {
      console.error('获取策略列表失败:', error)
      setError(error.message)
    }
  }

  // 开始状态轮询
  const startStatusPolling = () => {
    const pollStatus = async () => {
      try {
        const status = await apiService.getBacktestStatus()
        setBacktestStatus(status)

        // 根据状态更新结果和错误信息
        if (status.status === 'completed' && status.results) {
          setResults(status.results)
          setError('')
        } else if (status.status === 'error' && status.error_message) {
          setError(status.error_message)
          setResults(null)
        } else if (status.status === 'running') {
          setError('')
        }

        // 根据状态调整轮询间隔
        pollingManager.adjustPollingInterval(
          'backtest_status',
          pollStatus,
          status.status
        )
      } catch (error) {
        console.error('获取回测状态失败:', error)
        // 出错时使用默认间隔继续轮询
        pollingManager.adjustPollingInterval(
          'backtest_status',
          pollStatus,
          'error'
        )
      }
    }

    // 立即执行一次，然后开始轮询
    pollStatus()
  }

  const handleStartBacktest = async () => {
    if (!selectedStrategy || !startDate || !endDate) {
      setError('请选择策略和日期范围')
      return
    }

    if (new Date(startDate) >= new Date(endDate)) {
      setError('开始日期必须早于结束日期')
      return
    }

    setError('')
    setResults(null)

    try {
      const response = await apiService.startBacktest({
        strategy: selectedStrategy,
        start_date: startDate,
        end_date: endDate,
      })

      console.log('回测已开始:', response)

      // 开始回测后，立即更新状态轮询间隔
      const pollStatus = async () => {
        try {
          const status = await apiService.getBacktestStatus()
          setBacktestStatus(status)

          if (status.status === 'completed' && status.results) {
            setResults(status.results)
            setError('')
          } else if (status.status === 'error' && status.error_message) {
            setError(status.error_message)
            setResults(null)
          }

          pollingManager.adjustPollingInterval(
            'backtest_status',
            pollStatus,
            status.status
          )
        } catch (error) {
          console.error('获取回测状态失败:', error)
          pollingManager.adjustPollingInterval(
            'backtest_status',
            pollStatus,
            'error'
          )
        }
      }

      // 切换到运行状态的轮询间隔
      pollingManager.adjustPollingInterval(
        'backtest_status',
        pollStatus,
        'running'
      )
    } catch (error) {
      console.error('启动回测失败:', error)
      setError(error.message)
    }
  }

  return (
    <div className="space-y-6">
      {/* 错误提示 */}
      {error && (
        <div className="alert alert-error">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="stroke-current shrink-0 h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <span>{error}</span>
        </div>
      )}

      {/* 回测配置区域 */}
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h2 className="card-title">回测配置</h2>

          <BacktestConfigRow
            strategies={strategies}
            selectedStrategy={selectedStrategy}
            onStrategyChange={setSelectedStrategy}
            startDate={startDate}
            endDate={endDate}
            onStartDateChange={setStartDate}
            onEndDateChange={setEndDate}
            onStartBacktest={handleStartBacktest}
            isRunning={backtestStatus.is_running}
            disabled={backtestStatus.is_running}
          />
        </div>
      </div>

      {/* 回测状态 */}
      {(backtestStatus.is_running || backtestStatus.current_strategy) && (
        <BacktestStatus status={backtestStatus} />
      )}

      {/* 结果展示 */}
      {results && <ResultsDisplay results={results} />}
    </div>
  )
}

export default BacktestDashboard
