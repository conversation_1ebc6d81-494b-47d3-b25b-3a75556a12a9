import { useState, useEffect } from 'react'
import BacktestConfigRow from './BacktestConfigRow'
import BacktestStatus from './BacktestStatus'
import ResultsDisplay from './ResultsDisplay'

function BacktestDashboard({ socket }) {
  const [strategies, setStrategies] = useState([])
  const [selectedStrategy, setSelectedStrategy] = useState('')
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const [backtestStatus, setBacktestStatus] = useState({
    is_running: false,
    current_strategy: null,
    start_date: null,
    end_date: null,
    progress: 0,
    results: null,
  })
  const [results, setResults] = useState(null)
  const [error, setError] = useState('')

  useEffect(() => {
    if (!socket) return

    // 获取策略列表
    socket.emit('get_strategies')

    // 监听策略列表响应
    socket.on('strategies_list', (data) => {
      setStrategies(data.strategies || [])
    })

    // 监听回测状态更新
    socket.on('backtest_status', (status) => {
      setBacktestStatus(status)
    })

    // 监听回测开始
    socket.on('backtest_started', (data) => {
      setError('')
      console.log('回测已开始:', data)
    })

    // 监听回测完成
    socket.on('backtest_completed', (data) => {
      setResults(data.results)
      setBacktestStatus(data.status)
      console.log('回测完成:', data)
    })

    // 监听错误
    socket.on('error', (data) => {
      setError(data.message)
      console.error('服务器错误:', data.message)
    })

    socket.on('backtest_error', (data) => {
      setError(data.error)
      setBacktestStatus(data.status)
      console.error('回测错误:', data.error)
    })

    return () => {
      socket.off('strategies_list')
      socket.off('backtest_status')
      socket.off('backtest_started')
      socket.off('backtest_completed')
      socket.off('error')
      socket.off('backtest_error')
    }
  }, [socket])

  const handleStartBacktest = () => {
    if (!selectedStrategy || !startDate || !endDate) {
      setError('请选择策略和日期范围')
      return
    }

    if (new Date(startDate) >= new Date(endDate)) {
      setError('开始日期必须早于结束日期')
      return
    }

    setError('')
    setResults(null)

    socket.emit('start_backtest', {
      strategy: selectedStrategy,
      start_date: startDate,
      end_date: endDate,
    })
  }

  return (
    <div className="space-y-6">
      {/* 错误提示 */}
      {error && (
        <div className="alert alert-error">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="stroke-current shrink-0 h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <span>{error}</span>
        </div>
      )}

      {/* 回测配置区域 */}
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h2 className="card-title">回测配置</h2>

          <BacktestConfigRow
            strategies={strategies}
            selectedStrategy={selectedStrategy}
            onStrategyChange={setSelectedStrategy}
            startDate={startDate}
            endDate={endDate}
            onStartDateChange={setStartDate}
            onEndDateChange={setEndDate}
            onStartBacktest={handleStartBacktest}
            isRunning={backtestStatus.is_running}
            disabled={backtestStatus.is_running}
          />
        </div>
      </div>

      {/* 回测状态 */}
      {(backtestStatus.is_running || backtestStatus.current_strategy) && (
        <BacktestStatus status={backtestStatus} />
      )}

      {/* 结果展示 */}
      {results && <ResultsDisplay results={results} />}
    </div>
  )
}

export default BacktestDashboard
