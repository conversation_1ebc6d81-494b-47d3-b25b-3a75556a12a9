"""
回测结果统计计算
"""

import warnings
from typing import Any, Dict, Optional

import numpy as np
import pandas as pd

warnings.filterwarnings('ignore')


class Statistics:
    """统计计算类"""

    def __init__(self):
        self.trading_days_per_year = 252

    def calculate_performance(
        self, returns: pd.Series, benchmark_returns: Optional[pd.Series] = None
    ) -> Dict[str, Any]:
        """
        计算性能指标

        Args:
            returns: 策略收益率序列
            benchmark_returns: 基准收益率序列

        Returns:
            Dict[str, Any]: 性能指标字典
        """
        if returns.empty:
            return {}

        # 基本统计
        total_return = (1 + returns).prod() - 1
        annual_return = self._annualized_return(returns)
        annual_volatility = self._annualized_volatility(returns)
        sharpe_ratio = self._sharpe_ratio(returns)
        max_drawdown = self._max_drawdown(returns)

        # 胜率统计
        win_rate = (returns > 0).sum() / len(returns)

        # 收益分布
        positive_returns = returns[returns > 0]
        negative_returns = returns[returns < 0]

        avg_win = positive_returns.mean() if len(positive_returns) > 0 else 0
        avg_loss = negative_returns.mean() if len(negative_returns) > 0 else 0

        # 风险指标
        var_95 = returns.quantile(0.05)  # 95% VaR

        stats = {
            'total_return': total_return,
            'annual_return': annual_return,
            'annual_volatility': annual_volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'var_95': var_95,
            'total_trades': len(returns),
            'winning_trades': len(positive_returns),
            'losing_trades': len(negative_returns),
        }

        # 如果有基准数据，计算相对指标
        if benchmark_returns is not None and not benchmark_returns.empty:
            # 对齐数据
            aligned_returns, aligned_benchmark = returns.align(benchmark_returns, join='inner')

            if not aligned_returns.empty and not aligned_benchmark.empty:
                benchmark_total_return = (1 + aligned_benchmark).prod() - 1
                benchmark_annual_return = self._annualized_return(aligned_benchmark)
                benchmark_volatility = self._annualized_volatility(aligned_benchmark)

                # 超额收益
                excess_returns = aligned_returns - aligned_benchmark
                tracking_error = excess_returns.std() * np.sqrt(self.trading_days_per_year)
                information_ratio = (
                    excess_returns.mean()
                    / excess_returns.std()
                    * np.sqrt(self.trading_days_per_year)
                    if excess_returns.std() > 0
                    else 0
                )

                # Beta和Alpha
                beta = self._calculate_beta(aligned_returns, aligned_benchmark)
                alpha = annual_return - (
                    0.03 + beta * (benchmark_annual_return - 0.03)
                )  # 假设无风险利率3%

                stats.update(
                    {
                        'benchmark_total_return': benchmark_total_return,
                        'benchmark_annual_return': benchmark_annual_return,
                        'benchmark_volatility': benchmark_volatility,
                        'excess_return': total_return - benchmark_total_return,
                        'tracking_error': tracking_error,
                        'information_ratio': information_ratio,
                        'beta': beta,
                        'alpha': alpha,
                    }
                )

        return stats

    def _annualized_return(self, returns: pd.Series) -> float:
        """计算年化收益率"""
        if returns.empty:
            return 0.0

        total_return = (1 + returns).prod() - 1
        n_periods = len(returns)
        n_years = n_periods / self.trading_days_per_year

        if n_years <= 0:
            return 0.0

        return (1 + total_return) ** (1 / n_years) - 1

    def _annualized_volatility(self, returns: pd.Series) -> float:
        """计算年化波动率"""
        if returns.empty:
            return 0.0

        return returns.std() * np.sqrt(self.trading_days_per_year)

    def _sharpe_ratio(self, returns: pd.Series, risk_free_rate: float = 0.03) -> float:
        """计算夏普比率"""
        if returns.empty:
            return 0.0

        annual_return = self._annualized_return(returns)
        annual_volatility = self._annualized_volatility(returns)

        if annual_volatility == 0:
            return 0.0

        return (annual_return - risk_free_rate) / annual_volatility

    def _max_drawdown(self, returns: pd.Series) -> float:
        """计算最大回撤"""
        if returns.empty:
            return 0.0

        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max

        return drawdown.min()

    def _calculate_beta(self, returns: pd.Series, benchmark_returns: pd.Series) -> float:
        """计算Beta系数"""
        if returns.empty or benchmark_returns.empty:
            return 0.0

        covariance = np.cov(returns, benchmark_returns)[0][1]
        benchmark_variance = np.var(benchmark_returns)

        if benchmark_variance == 0:
            return 0.0

        return covariance / benchmark_variance

    def calculate_trade_stats(self, trade_records: list) -> Dict[str, Any]:
        """
        计算交易统计

        Args:
            trade_records: 交易记录列表

        Returns:
            Dict[str, Any]: 交易统计
        """
        if not trade_records:
            return {}

        df = pd.DataFrame(trade_records)

        # 按股票分组统计
        by_symbol = (
            df.groupby('symbol')
            .agg({'quantity': 'sum', 'price': 'mean', 'commission': 'sum'})
            .round(4)
        )

        # 买卖统计
        buy_trades = df[df['side'] == 'buy']
        sell_trades = df[df['side'] == 'sell']

        stats = {
            'total_trades': len(df),
            'buy_trades': len(buy_trades),
            'sell_trades': len(sell_trades),
            'total_commission': df['commission'].sum(),
            'avg_commission_per_trade': df['commission'].mean(),
            'trade_by_symbol': by_symbol.to_dict('index'),
        }

        return stats

    def calculate_monthly_returns(self, portfolio_history: pd.DataFrame) -> pd.DataFrame:
        """
        计算月度收益率

        Args:
            portfolio_history: 投资组合历史数据

        Returns:
            pd.DataFrame: 月度收益率
        """
        if portfolio_history.empty:
            return pd.DataFrame()

        df = portfolio_history.copy()
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.set_index('timestamp')

        # 按月重采样
        monthly = df['total_value'].resample('M').last()
        monthly_returns = monthly.pct_change().dropna()

        # 创建月度统计表
        monthly_stats = pd.DataFrame(
            {
                'month': monthly_returns.index.strftime('%Y-%m'),
                'return': monthly_returns.values,
                'cumulative_return': (1 + monthly_returns).cumprod() - 1,
            }
        )

        return monthly_stats

    def calculate_yearly_returns(self, portfolio_history: pd.DataFrame) -> pd.DataFrame:
        """
        计算年度收益率

        Args:
            portfolio_history: 投资组合历史数据

        Returns:
            pd.DataFrame: 年度收益率
        """
        if portfolio_history.empty:
            return pd.DataFrame()

        df = portfolio_history.copy()
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.set_index('timestamp')

        # 按年重采样
        yearly = df['total_value'].resample('Y').last()
        yearly_returns = yearly.pct_change().dropna()

        # 创建年度统计表
        yearly_stats = pd.DataFrame(
            {
                'year': yearly_returns.index.year,
                'return': yearly_returns.values,
                'cumulative_return': (1 + yearly_returns).cumprod() - 1,
            }
        )

        return yearly_stats
