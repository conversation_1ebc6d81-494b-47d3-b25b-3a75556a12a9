/* tailwind配置, 不要删除*/
@import 'tailwindcss' source('../src');
@plugin "daisyui";

/* 自定义样式 */
.table th {
  background-color: hsl(var(--b2));
  font-weight: 600;
}

.table-zebra tbody tr:nth-child(even) {
  background-color: hsl(var(--b2) / 0.5);
}

/* 图表容器样式 */
.highcharts-container {
  font-family: inherit !important;
}

/* 响应式表格 */
@media (max-width: 768px) {
  .table {
    font-size: 0.875rem;
  }

  .stat-value {
    font-size: 1.5rem !important;
  }
}

/* 加载动画 */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 滚动条样式 */
.overflow-x-auto::-webkit-scrollbar {
  height: 8px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: hsl(var(--b3));
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: hsl(var(--bc) / 0.3);
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--bc) / 0.5);
}
