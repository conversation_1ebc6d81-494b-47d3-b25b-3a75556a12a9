import { useState } from 'react'

function PortfolioTable({ portfolioHistory }) {
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(20)

  if (!portfolioHistory || !portfolioHistory.dates || portfolioHistory.dates.length === 0) {
    return (
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h2 className="card-title">每日市值</h2>
          <p className="text-gray-500">暂无数据</p>
        </div>
      </div>
    )
  }

  const { dates, total_value, available_cash, returns, cumulative_returns } = portfolioHistory

  // 准备表格数据
  const tableData = dates.map((date, index) => ({
    date,
    total_value: total_value[index] || 0,
    available_cash: available_cash[index] || 0,
    returns: returns[index] || 0,
    cumulative_returns: cumulative_returns[index] || 0
  })).reverse() // 最新日期在前

  // 分页逻辑
  const totalPages = Math.ceil(tableData.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentData = tableData.slice(startIndex, endIndex)

  const formatCurrency = (value) => {
    return `¥${value.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
  }

  const formatPercent = (value) => {
    return `${(value * 100).toFixed(2)}%`
  }

  const getReturnColor = (value) => {
    if (value > 0) return 'text-success'
    if (value < 0) return 'text-error'
    return 'text-gray-500'
  }

  return (
    <div className="card bg-base-100 shadow-xl">
      <div className="card-body">
        <div className="flex justify-between items-center mb-4">
          <h2 className="card-title">每日市值明细</h2>
          <div className="text-sm text-gray-500">
            共 {tableData.length} 条记录
          </div>
        </div>

        {/* 表格 */}
        <div className="overflow-x-auto">
          <table className="table table-zebra w-full">
            <thead>
              <tr>
                <th>日期</th>
                <th>总市值</th>
                <th>可用资金</th>
                <th>当日收益率</th>
                <th>累积收益率</th>
              </tr>
            </thead>
            <tbody>
              {currentData.map((row, index) => (
                <tr key={index}>
                  <td className="font-mono">{row.date}</td>
                  <td className="font-mono">{formatCurrency(row.total_value)}</td>
                  <td className="font-mono">{formatCurrency(row.available_cash)}</td>
                  <td className={`font-mono ${getReturnColor(row.returns)}`}>
                    {formatPercent(row.returns)}
                  </td>
                  <td className={`font-mono ${getReturnColor(row.cumulative_returns)}`}>
                    {formatPercent(row.cumulative_returns)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* 分页控件 */}
        {totalPages > 1 && (
          <div className="flex justify-center mt-4">
            <div className="join">
              <button
                className="join-item btn btn-sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                «
              </button>
              
              {/* 页码按钮 */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum
                if (totalPages <= 5) {
                  pageNum = i + 1
                } else if (currentPage <= 3) {
                  pageNum = i + 1
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i
                } else {
                  pageNum = currentPage - 2 + i
                }
                
                return (
                  <button
                    key={pageNum}
                    className={`join-item btn btn-sm ${currentPage === pageNum ? 'btn-active' : ''}`}
                    onClick={() => setCurrentPage(pageNum)}
                  >
                    {pageNum}
                  </button>
                )
              })}
              
              <button
                className="join-item btn btn-sm"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
              >
                »
              </button>
            </div>
          </div>
        )}

        {/* 分页信息 */}
        <div className="text-center text-sm text-gray-500 mt-2">
          第 {currentPage} 页，共 {totalPages} 页
        </div>
      </div>
    </div>
  )
}

export default PortfolioTable
