import { useState } from 'react'

function TradeRecords({ trades }) {
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(20)
  const [filterSymbol, setFilterSymbol] = useState('')
  const [filterAction, setFilterAction] = useState('')

  if (!trades || trades.length === 0) {
    return (
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h2 className="card-title">成交记录</h2>
          <div className="text-center py-8">
            <p className="text-gray-500">无成交记录</p>
          </div>
        </div>
      </div>
    )
  }

  // 过滤数据
  const filteredTrades = trades.filter(trade => {
    const symbolMatch = !filterSymbol || trade.symbol.toLowerCase().includes(filterSymbol.toLowerCase())
    const actionMatch = !filterAction || trade.action === filterAction
    return symbolMatch && actionMatch
  }).sort((a, b) => new Date(b.date) - new Date(a.date)) // 最新的在前

  // 分页逻辑
  const totalPages = Math.ceil(filteredTrades.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentData = filteredTrades.slice(startIndex, endIndex)

  // 获取唯一的股票代码和操作类型
  const uniqueSymbols = [...new Set(trades.map(trade => trade.symbol))].sort()
  const uniqueActions = [...new Set(trades.map(trade => trade.action))].sort()

  const formatCurrency = (value) => {
    return `¥${value.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
  }

  const formatNumber = (value) => {
    return value.toLocaleString('zh-CN')
  }

  const getActionColor = (action) => {
    switch (action.toLowerCase()) {
      case 'buy':
      case '买入':
        return 'text-success'
      case 'sell':
      case '卖出':
        return 'text-error'
      default:
        return 'text-gray-500'
    }
  }

  const getActionBadge = (action) => {
    switch (action.toLowerCase()) {
      case 'buy':
      case '买入':
        return 'badge-success'
      case 'sell':
      case '卖出':
        return 'badge-error'
      default:
        return 'badge-neutral'
    }
  }

  // 重置分页当过滤条件改变时
  const handleFilterChange = (type, value) => {
    setCurrentPage(1)
    if (type === 'symbol') {
      setFilterSymbol(value)
    } else if (type === 'action') {
      setFilterAction(value)
    }
  }

  return (
    <div className="card bg-base-100 shadow-xl">
      <div className="card-body">
        <div className="flex justify-between items-center mb-4">
          <h2 className="card-title">成交记录</h2>
          <div className="text-sm text-gray-500">
            共 {filteredTrades.length} 条记录
          </div>
        </div>

        {/* 过滤器 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="form-control">
            <label className="label">
              <span className="label-text">股票代码</span>
            </label>
            <select
              className="select select-bordered select-sm"
              value={filterSymbol}
              onChange={(e) => handleFilterChange('symbol', e.target.value)}
            >
              <option value="">全部</option>
              {uniqueSymbols.map(symbol => (
                <option key={symbol} value={symbol}>{symbol}</option>
              ))}
            </select>
          </div>

          <div className="form-control">
            <label className="label">
              <span className="label-text">操作类型</span>
            </label>
            <select
              className="select select-bordered select-sm"
              value={filterAction}
              onChange={(e) => handleFilterChange('action', e.target.value)}
            >
              <option value="">全部</option>
              {uniqueActions.map(action => (
                <option key={action} value={action}>{action}</option>
              ))}
            </select>
          </div>

          <div className="form-control">
            <label className="label">
              <span className="label-text">操作</span>
            </label>
            <button
              className="btn btn-outline btn-sm"
              onClick={() => {
                setFilterSymbol('')
                setFilterAction('')
                setCurrentPage(1)
              }}
            >
              清除过滤
            </button>
          </div>
        </div>

        {/* 表格 */}
        <div className="overflow-x-auto">
          <table className="table table-zebra w-full">
            <thead>
              <tr>
                <th>日期</th>
                <th>股票代码</th>
                <th>操作</th>
                <th>数量</th>
                <th>价格</th>
                <th>金额</th>
                <th>手续费</th>
              </tr>
            </thead>
            <tbody>
              {currentData.map((trade, index) => (
                <tr key={index}>
                  <td className="font-mono">{trade.date}</td>
                  <td className="font-mono font-semibold">{trade.symbol}</td>
                  <td>
                    <div className={`badge ${getActionBadge(trade.action)}`}>
                      {trade.action}
                    </div>
                  </td>
                  <td className="font-mono">{formatNumber(Math.abs(trade.volume))}</td>
                  <td className="font-mono">{formatCurrency(trade.price)}</td>
                  <td className={`font-mono ${getActionColor(trade.action)}`}>
                    {formatCurrency(Math.abs(trade.amount))}
                  </td>
                  <td className="font-mono text-warning">{formatCurrency(trade.commission)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* 分页控件 */}
        {totalPages > 1 && (
          <div className="flex justify-center mt-4">
            <div className="join">
              <button
                className="join-item btn btn-sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                «
              </button>
              
              {/* 页码按钮 */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum
                if (totalPages <= 5) {
                  pageNum = i + 1
                } else if (currentPage <= 3) {
                  pageNum = i + 1
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i
                } else {
                  pageNum = currentPage - 2 + i
                }
                
                return (
                  <button
                    key={pageNum}
                    className={`join-item btn btn-sm ${currentPage === pageNum ? 'btn-active' : ''}`}
                    onClick={() => setCurrentPage(pageNum)}
                  >
                    {pageNum}
                  </button>
                )
              })}
              
              <button
                className="join-item btn btn-sm"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
              >
                »
              </button>
            </div>
          </div>
        )}

        {/* 分页信息 */}
        <div className="text-center text-sm text-gray-500 mt-2">
          第 {currentPage} 页，共 {totalPages} 页
        </div>
      </div>
    </div>
  )
}

export default TradeRecords
