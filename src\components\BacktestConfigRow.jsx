import { DateInput } from '@mantine/dates'

function BacktestConfigRow({
  strategies,
  selectedStrategy,
  onStrategyChange,
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  onStartBacktest,
  isRunning,
  disabled,
}) {
  // 获取今天的日期作为默认值
  const today = new Date()

  const canStart = !!(selectedStrategy && startDate && endDate)

  return (
    <div className="grid grid-cols-1 lg:grid-cols-5 gap-4 items-end">
      {/* 策略选择 */}
      <div className="form-control">
        <label className="label">
          <span className="label-text font-semibold">选择策略</span>
        </label>
        <select
          className="select select-bordered w-full"
          value={selectedStrategy}
          onChange={(e) => onStrategyChange(e.target.value)}
          disabled={disabled}
        >
          <option value="">请选择策略文件</option>
          {strategies.map((strategy) => (
            <option key={strategy.name} value={strategy.name}>
              {strategy.filename}
            </option>
          ))}
        </select>
      </div>

      {/* 开始日期 */}
      <div>
        <DateInput
          label="开始日期"
          placeholder="选择开始日期"
          value={startDate}
          onChange={onStartDateChange}
          disabled={disabled}
          maxDate={today}
          size="md"
          valueFormat="YYYY-MM-DD"
          styles={{
            label: { fontWeight: 600 },
          }}
        />
      </div>

      {/* 结束日期 */}
      <div>
        <DateInput
          label="结束日期"
          placeholder="选择结束日期"
          value={endDate}
          onChange={onEndDateChange}
          disabled={disabled}
          minDate={startDate ? new Date(startDate) : undefined}
          maxDate={today}
          size="md"
          valueFormat="YYYY-MM-DD"
          styles={{
            label: { fontWeight: 600 },
          }}
        />
      </div>

      {/* 回测期间显示 */}
      <div className="text-center">
        {startDate && endDate && (
          <div className="text-sm text-blue-600">
            回测期间:{' '}
            {Math.ceil(
              (new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24)
            )}{' '}
            天
          </div>
        )}
      </div>

      {/* 开始回测按钮 */}
      <div className="flex flex-col">
        <button
          className={`btn btn-primary btn-lg ${isRunning ? 'loading' : ''}`}
          onClick={onStartBacktest}
          disabled={!canStart || isRunning}
        >
          {isRunning ? (
            <>
              <span className="loading loading-spinner"></span>
              回测运行中...
            </>
          ) : (
            '开始回测'
          )}
        </button>

        {!canStart && !isRunning && (
          <div className="text-sm text-orange-500 mt-2 text-center">
            请先选择策略和日期范围
          </div>
        )}
      </div>
    </div>
  )
}

export default BacktestConfigRow
