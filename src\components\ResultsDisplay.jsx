import { useState } from 'react'
import ReturnsChart from './ReturnsChart'
import PerformanceStats from './PerformanceStats'
import PortfolioTable from './PortfolioTable'
import TradeRecords from './TradeRecords'
import PositionsTable from './PositionsTable'

function ResultsDisplay({ results }) {
  const [activeTab, setActiveTab] = useState('overview')

  if (!results) {
    return null
  }

  const tabs = [
    { id: 'overview', name: '概览', icon: '📊' },
    { id: 'portfolio', name: '每日市值', icon: '💰' },
    { id: 'positions', name: '持仓明细', icon: '📈' },
    { id: 'trades', name: '成交记录', icon: '📋' }
  ]

  return (
    <div className="space-y-6">
      {/* 标签页导航 */}
      <div className="tabs tabs-boxed bg-base-100 shadow-lg">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`tab tab-lg ${activeTab === tab.id ? 'tab-active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <span className="mr-2">{tab.icon}</span>
            {tab.name}
          </button>
        ))}
      </div>

      {/* 标签页内容 */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* 收益曲线图 */}
          <ReturnsChart portfolioHistory={results.portfolio_history} />
          
          {/* 性能统计 */}
          <PerformanceStats stats={results.performance_stats} />
        </div>
      )}

      {activeTab === 'portfolio' && (
        <PortfolioTable portfolioHistory={results.portfolio_history} />
      )}

      {activeTab === 'positions' && (
        <PositionsTable positions={results.final_positions} />
      )}

      {activeTab === 'trades' && (
        <TradeRecords trades={results.trade_records} />
      )}
    </div>
  )
}

export default ResultsDisplay
