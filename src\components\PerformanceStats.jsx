function PerformanceStats({ stats }) {
  if (!stats) {
    return (
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h2 className="card-title">回测统计</h2>
          <p className="text-gray-500">暂无统计数据</p>
        </div>
      </div>
    )
  }

  const formatPercent = (value) => {
    if (value === null || value === undefined || isNaN(value)) return 'N/A'
    return `${(value * 100).toFixed(2)}%`
  }

  const formatNumber = (value, decimals = 2) => {
    if (value === null || value === undefined || isNaN(value)) return 'N/A'
    return value.toFixed(decimals)
  }

  const formatCurrency = (value) => {
    if (value === null || value === undefined || isNaN(value)) return 'N/A'
    return `¥${value.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
  }

  const getReturnColor = (value) => {
    if (value === null || value === undefined || isNaN(value)) return 'text-gray-500'
    return value >= 0 ? 'text-success' : 'text-error'
  }

  return (
    <div className="card bg-base-100 shadow-xl">
      <div className="card-body">
        <h2 className="card-title">回测统计指标</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* 收益指标 */}
          <div className="stat bg-base-200 rounded-lg">
            <div className="stat-title">总收益率</div>
            <div className={`stat-value text-2xl ${getReturnColor(stats.total_return)}`}>
              {formatPercent(stats.total_return)}
            </div>
            <div className="stat-desc">整个回测期间的总收益</div>
          </div>

          <div className="stat bg-base-200 rounded-lg">
            <div className="stat-title">年化收益率</div>
            <div className={`stat-value text-2xl ${getReturnColor(stats.annual_return)}`}>
              {formatPercent(stats.annual_return)}
            </div>
            <div className="stat-desc">年化后的收益率</div>
          </div>

          <div className="stat bg-base-200 rounded-lg">
            <div className="stat-title">最大回撤</div>
            <div className="stat-value text-2xl text-error">
              {formatPercent(stats.max_drawdown)}
            </div>
            <div className="stat-desc">最大的资产回撤幅度</div>
          </div>

          <div className="stat bg-base-200 rounded-lg">
            <div className="stat-title">夏普比率</div>
            <div className={`stat-value text-2xl ${stats.sharpe_ratio >= 1 ? 'text-success' : stats.sharpe_ratio >= 0 ? 'text-warning' : 'text-error'}`}>
              {formatNumber(stats.sharpe_ratio)}
            </div>
            <div className="stat-desc">风险调整后收益</div>
          </div>

          {/* 风险指标 */}
          <div className="stat bg-base-200 rounded-lg">
            <div className="stat-title">年化波动率</div>
            <div className="stat-value text-2xl">
              {formatPercent(stats.annual_volatility)}
            </div>
            <div className="stat-desc">收益率的年化标准差</div>
          </div>

          <div className="stat bg-base-200 rounded-lg">
            <div className="stat-title">95% VaR</div>
            <div className="stat-value text-2xl text-warning">
              {formatPercent(stats.var_95)}
            </div>
            <div className="stat-desc">95%置信度风险价值</div>
          </div>

          <div className="stat bg-base-200 rounded-lg">
            <div className="stat-title">胜率</div>
            <div className="stat-value text-2xl">
              {formatPercent(stats.win_rate)}
            </div>
            <div className="stat-desc">盈利交易日占比</div>
          </div>

          <div className="stat bg-base-200 rounded-lg">
            <div className="stat-title">盈亏比</div>
            <div className="stat-value text-2xl">
              {formatNumber(stats.profit_loss_ratio)}
            </div>
            <div className="stat-desc">平均盈利/平均亏损</div>
          </div>
        </div>

        {/* 额外信息 */}
        {(stats.total_trades !== undefined || stats.benchmark_return !== undefined) && (
          <div className="divider">其他信息</div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {stats.total_trades !== undefined && (
            <div className="stat bg-base-200 rounded-lg">
              <div className="stat-title">总交易次数</div>
              <div className="stat-value text-xl">
                {stats.total_trades}
              </div>
            </div>
          )}

          {stats.benchmark_return !== undefined && (
            <div className="stat bg-base-200 rounded-lg">
              <div className="stat-title">基准收益率</div>
              <div className={`stat-value text-xl ${getReturnColor(stats.benchmark_return)}`}>
                {formatPercent(stats.benchmark_return)}
              </div>
            </div>
          )}

          {stats.alpha !== undefined && (
            <div className="stat bg-base-200 rounded-lg">
              <div className="stat-title">Alpha</div>
              <div className={`stat-value text-xl ${getReturnColor(stats.alpha)}`}>
                {formatPercent(stats.alpha)}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default PerformanceStats
