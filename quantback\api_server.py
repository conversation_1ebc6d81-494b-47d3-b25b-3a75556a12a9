#!/usr/bin/env python3
"""
量化回测FastAPI服务器
提供REST API接口，支持策略选择、回测执行、状态查询等功能
"""

import asyncio
import logging
import os
import sys
import uuid
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import uvicorn
from fastapi import BackgroundTasks, FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from quantback.log_setup import setup_logging
from quantback.port_bt.engine import BacktestEngine

# 设置日志
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(title='QuantBack 回测API', description='量化回测系统REST API接口', version='1.0.0')

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=['*'],  # 在生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=['*'],
    allow_headers=['*'],
)

# 线程池用于执行耗时的回测任务
executor = ThreadPoolExecutor(max_workers=2)


# 全局状态管理
class BacktestState:
    def __init__(self):
        self.is_running = False
        self.current_strategy = None
        self.start_date = None
        self.end_date = None
        self.progress = 0
        self.status = 'idle'  # idle, running, completed, error
        self.error_message = None
        self.results = None
        self.task_id = None


backtest_state = BacktestState()


# 请求/响应模型
class BacktestRequest(BaseModel):
    strategy: str
    start_date: str
    end_date: str


class BacktestResponse(BaseModel):
    message: str
    task_id: str


class BacktestStatus(BaseModel):
    is_running: bool
    current_strategy: Optional[str]
    start_date: Optional[str]
    end_date: Optional[str]
    progress: int
    status: str
    error_message: Optional[str]
    results: Optional[Dict[str, Any]]


class StrategyInfo(BaseModel):
    filename: str
    name: str
    path: str


class StrategiesResponse(BaseModel):
    strategies: List[StrategyInfo]


class HealthResponse(BaseModel):
    status: str
    server_time: str


class ErrorResponse(BaseModel):
    error: bool
    message: str
    code: Optional[str] = None


def get_strategy_files() -> List[Dict[str, str]]:
    """获取策略文件列表"""
    strategies_dir = Path(__file__).parent / 'port_bt' / 'strategies'
    strategy_files = []

    if strategies_dir.exists():
        for py_file in strategies_dir.glob('*.py'):
            if py_file.name != '__init__.py':
                strategy_files.append(
                    {'filename': py_file.name, 'name': py_file.stem, 'path': str(py_file)}
                )

    return strategy_files


def load_strategy_module(strategy_path: str):
    """动态加载策略模块"""
    import importlib.util

    spec = importlib.util.spec_from_file_location('strategy', strategy_path)
    if spec is None or spec.loader is None:
        raise ValueError(f'无法加载策略文件: {strategy_path}')

    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module


def run_backtest_sync(strategy_path: str, start_date: str, end_date: str, task_id: str):
    """同步运行回测（在线程池中执行）"""
    global backtest_state

    try:
        # 更新状态
        backtest_state.is_running = True
        backtest_state.progress = 0
        backtest_state.status = 'running'
        backtest_state.error_message = None

        # 加载策略模块
        strategy_module = load_strategy_module(strategy_path)

        if not hasattr(strategy_module, 'initialize'):
            raise ValueError('策略文件必须包含initialize函数')

        if not hasattr(strategy_module, 'STRATEGY_CONFIG'):
            raise ValueError('策略文件必须包含STRATEGY_CONFIG配置')

        # 获取策略配置并更新日期
        strategy_config = strategy_module.STRATEGY_CONFIG.copy()
        strategy_config['start_date'] = start_date
        strategy_config['end_date'] = end_date

        # 创建回测引擎
        engine = BacktestEngine(
            initialize_func=strategy_module.initialize, strategy_config=strategy_config
        )

        # 运行回测
        results = engine.run()

        # 处理结果数据，转换为可序列化的格式
        processed_results = process_backtest_results(results)

        # 更新状态
        backtest_state.is_running = False
        backtest_state.progress = 100
        backtest_state.status = 'completed'
        backtest_state.results = processed_results

        logger.info(f'回测完成: {task_id}')
        return processed_results

    except Exception as e:
        logger.error(f'回测执行错误: {e}')
        backtest_state.is_running = False
        backtest_state.status = 'error'
        backtest_state.error_message = str(e)
        raise e


def process_backtest_results(results: Dict[str, Any]) -> Dict[str, Any]:
    """处理回测结果，转换为前端可用的格式"""
    try:
        processed = {}

        # 处理投资组合历史数据
        if 'portfolio_history' in results:
            portfolio_df = results['portfolio_history']

            # 处理日期索引 - 检查索引类型
            if hasattr(portfolio_df.index, 'strftime'):
                # 如果是DatetimeIndex
                dates = portfolio_df.index.strftime('%Y-%m-%d').tolist()
            else:
                # 如果是其他类型的索引，尝试转换为字符串
                dates = [str(date) for date in portfolio_df.index]

            processed['portfolio_history'] = {
                'dates': dates,
                'total_value': portfolio_df['total_value'].tolist(),
                'available_cash': portfolio_df['available_cash'].tolist(),
                'returns': portfolio_df['returns'].fillna(0).tolist(),
                'cumulative_returns': (portfolio_df['returns'].fillna(0).cumsum()).tolist(),
            }

        # 处理性能统计
        if 'performance_stats' in results:
            processed['performance_stats'] = results['performance_stats']

        # 处理交易记录
        if 'trade_records' in results:
            trade_records = results['trade_records']
            processed['trade_records'] = [
                {
                    'datetime': record.datetime.isoformat()
                    if hasattr(record.datetime, 'isoformat')
                    else str(record.datetime),
                    'symbol': record.symbol,
                    'action': record.action,
                    'volume': record.volume,
                    'price': record.price,
                    'amount': record.amount,
                    'commission': record.commission,
                    'tax': record.tax,
                }
                for record in trade_records
            ]

        # 处理最终持仓
        if 'final_positions' in results:
            positions = results['final_positions']
            processed['final_positions'] = [
                {
                    'symbol': symbol,
                    'volume': pos.total_volume,
                    'available_volume': pos.closeable_volume,
                    'avg_cost': pos.avg_cost,
                    'market_value': pos.value,
                }
                for symbol, pos in positions.items()
                if pos.total_volume > 0
            ]

        return processed

    except Exception as e:
        logger.error(f'处理回测结果错误: {e}')
        return {'error': str(e)}


# API路由定义
@app.get('/health', response_model=HealthResponse)
async def health_check():
    """健康检查"""
    return HealthResponse(status='ok', server_time=datetime.now().isoformat())


@app.get('/api/v1/strategies', response_model=StrategiesResponse)
async def get_strategies():
    """获取策略列表"""
    try:
        strategies = get_strategy_files()
        return StrategiesResponse(strategies=[StrategyInfo(**strategy) for strategy in strategies])
    except Exception as e:
        logger.error(f'获取策略列表错误: {e}')
        raise HTTPException(status_code=500, detail=f'获取策略列表失败: {str(e)}')


@app.post('/api/v1/backtest/start', response_model=BacktestResponse)
async def start_backtest(request: BacktestRequest, background_tasks: BackgroundTasks):
    """开始回测"""
    global backtest_state

    try:
        # 检查是否已有回测在运行
        if backtest_state.is_running:
            raise HTTPException(status_code=409, detail='已有回测任务在运行中')

        # 查找策略文件
        strategies = get_strategy_files()
        strategy_file = None
        for strategy in strategies:
            if strategy['name'] == request.strategy:
                strategy_file = strategy['path']
                break

        if not strategy_file:
            raise HTTPException(status_code=404, detail=f'未找到策略文件: {request.strategy}')

        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 更新状态
        backtest_state.current_strategy = request.strategy
        backtest_state.start_date = request.start_date
        backtest_state.end_date = request.end_date
        backtest_state.task_id = task_id
        backtest_state.results = None
        backtest_state.error_message = None

        # 在后台执行回测
        background_tasks.add_task(
            run_backtest_in_background, strategy_file, request.start_date, request.end_date, task_id
        )

        return BacktestResponse(message='回测已开始', task_id=task_id)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'启动回测错误: {e}')
        raise HTTPException(status_code=500, detail=f'启动回测失败: {str(e)}')


async def run_backtest_in_background(
    strategy_path: str, start_date: str, end_date: str, task_id: str
):
    """在后台运行回测"""
    loop = asyncio.get_event_loop()
    try:
        await loop.run_in_executor(
            executor, run_backtest_sync, strategy_path, start_date, end_date, task_id
        )
    except Exception as e:
        logger.error(f'后台回测任务执行失败: {e}')


@app.get('/api/v1/backtest/status', response_model=BacktestStatus)
async def get_backtest_status():
    """获取回测状态"""
    return BacktestStatus(
        is_running=backtest_state.is_running,
        current_strategy=backtest_state.current_strategy,
        start_date=backtest_state.start_date,
        end_date=backtest_state.end_date,
        progress=backtest_state.progress,
        status=backtest_state.status,
        error_message=backtest_state.error_message,
        results=backtest_state.results,
    )


@app.post('/api/v1/backtest/stop')
async def stop_backtest():
    """停止回测"""
    global backtest_state

    if not backtest_state.is_running:
        raise HTTPException(status_code=400, detail='当前没有运行中的回测任务')

    # 注意：这里只是更新状态，实际的线程停止需要更复杂的实现
    # 在生产环境中，可能需要使用可取消的任务或其他机制
    backtest_state.is_running = False
    backtest_state.status = 'stopped'

    return {'message': '回测已停止'}


if __name__ == '__main__':
    # 设置日志
    setup_logging()

    print('=' * 60)
    print('QuantBack FastAPI 回测服务器')
    print('=' * 60)
    print(f'启动时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    print('=' * 60)

    # 启动服务器
    uvicorn.run(app, host='0.0.0.0', port=8000, log_level='info')
