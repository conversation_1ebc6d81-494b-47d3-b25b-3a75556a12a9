#!/usr/bin/env python3
"""
量化回测FastAPI服务器
提供REST API接口，支持策略选择、回测执行、状态查询等功能
"""

import asyncio
import logging
import os
import sys
import uuid
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import uvicorn
from fastapi import BackgroundTasks, FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from quantback.log_setup import setup_logging
from quantback.port_bt.engine import BacktestEngine

# 设置日志
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(title='QuantBack 回测API', description='量化回测系统REST API接口', version='1.0.0')

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=['*'],  # 在生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=['*'],
    allow_headers=['*'],
)


# 应用启动和关闭事件
@app.on_event('startup')
async def startup_event():
    """应用启动时的初始化"""
    global cleanup_task
    # 启动定期清理任务
    cleanup_task = asyncio.create_task(cleanup_connections_periodically())
    logger.info('WebSocket连接清理任务已启动')


@app.on_event('shutdown')
async def shutdown_event():
    """应用关闭时的清理"""
    global cleanup_task
    if cleanup_task:
        cleanup_task.cancel()
        try:
            await cleanup_task
        except asyncio.CancelledError:
            pass
    logger.info('WebSocket连接清理任务已停止')


# 线程池用于执行耗时的回测任务
executor = ThreadPoolExecutor(max_workers=2)


# WebSocket连接管理器
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f'WebSocket连接已建立，当前连接数: {len(self.active_connections)}')

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
            logger.info(f'WebSocket连接已断开，当前连接数: {len(self.active_connections)}')

    async def send_personal_message(self, message: dict, websocket: WebSocket):
        try:
            await websocket.send_json(message)
        except Exception as e:
            logger.error(f'发送WebSocket消息失败: {e}')
            self.disconnect(websocket)

    def is_connection_alive(self, websocket: WebSocket) -> bool:
        """检查WebSocket连接是否仍然有效"""
        try:
            # 检查连接状态
            return websocket.client_state.value == 1  # CONNECTED
        except Exception:
            return False

    async def cleanup_dead_connections(self):
        """清理已断开的连接"""
        dead_connections = []
        for connection in self.active_connections:
            if not self.is_connection_alive(connection):
                dead_connections.append(connection)

        for connection in dead_connections:
            self.disconnect(connection)

        if dead_connections:
            logger.info(f'清理了 {len(dead_connections)} 个无效连接')

    async def broadcast(self, message: dict):
        """广播消息给所有连接的客户端"""
        if not self.active_connections:
            return

        # 先清理无效连接
        await self.cleanup_dead_connections()

        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_json(message)
            except Exception as e:
                logger.error(f'广播WebSocket消息失败: {e}')
                disconnected.append(connection)

        # 清理发送失败的连接
        for connection in disconnected:
            self.disconnect(connection)


manager = ConnectionManager()


# 定期清理无效连接的后台任务
async def cleanup_connections_periodically():
    """定期清理无效的WebSocket连接"""
    while True:
        try:
            await asyncio.sleep(30)  # 每30秒检查一次
            await manager.cleanup_dead_connections()
        except Exception as e:
            logger.error(f'定期清理连接任务错误: {e}')


# 启动定期清理任务
cleanup_task = None


# 全局状态管理
class BacktestState:
    def __init__(self):
        self.is_running = False
        self.current_strategy = None
        self.start_date = None
        self.end_date = None
        self.progress = 0
        self.status = 'idle'  # idle, running, completed, error
        self.error_message = None
        self.results = None
        self.task_id = None

    async def update_status(self, **kwargs):
        """更新状态并广播给所有WebSocket连接"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

        # 广播状态更新
        status_message = {
            'type': 'status_update',
            'data': {
                'is_running': self.is_running,
                'current_strategy': self.current_strategy,
                'start_date': self.start_date,
                'end_date': self.end_date,
                'progress': self.progress,
                'status': self.status,
                'error_message': self.error_message,
                'results': self.results,
                'task_id': self.task_id,
            },
        }
        await manager.broadcast(status_message)

    def to_dict(self):
        """转换为字典格式"""
        return {
            'is_running': self.is_running,
            'current_strategy': self.current_strategy,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'progress': self.progress,
            'status': self.status,
            'error_message': self.error_message,
            'results': self.results,
            'task_id': self.task_id,
        }


backtest_state = BacktestState()


# 请求/响应模型
class BacktestRequest(BaseModel):
    strategy: str
    start_date: str
    end_date: str


class BacktestResponse(BaseModel):
    message: str
    task_id: str


class StrategyInfo(BaseModel):
    filename: str
    name: str
    path: str


class StrategiesResponse(BaseModel):
    strategies: List[StrategyInfo]


class ErrorResponse(BaseModel):
    error: bool
    message: str
    code: Optional[str] = None


def get_strategy_files() -> List[Dict[str, str]]:
    """获取策略文件列表"""
    strategies_dir = Path(__file__).parent / 'port_bt' / 'strategies'
    strategy_files = []

    if strategies_dir.exists():
        for py_file in strategies_dir.glob('*.py'):
            if py_file.name != '__init__.py':
                strategy_files.append(
                    {'filename': py_file.name, 'name': py_file.stem, 'path': str(py_file)}
                )

    return strategy_files


def load_strategy_module(strategy_path: str):
    """动态加载策略模块"""
    import importlib.util

    spec = importlib.util.spec_from_file_location('strategy', strategy_path)
    if spec is None or spec.loader is None:
        raise ValueError(f'无法加载策略文件: {strategy_path}')

    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module


def run_backtest_sync(strategy_path: str, start_date: str, end_date: str, task_id: str):
    """同步运行回测（在线程池中执行）"""
    global backtest_state

    try:
        # 加载策略模块
        strategy_module = load_strategy_module(strategy_path)

        if not hasattr(strategy_module, 'initialize'):
            raise ValueError('策略文件必须包含initialize函数')

        if not hasattr(strategy_module, 'STRATEGY_CONFIG'):
            raise ValueError('策略文件必须包含STRATEGY_CONFIG配置')

        # 获取策略配置并更新日期
        strategy_config = strategy_module.STRATEGY_CONFIG.copy()
        strategy_config['start_date'] = start_date
        strategy_config['end_date'] = end_date

        # 创建回测引擎
        engine = BacktestEngine(
            initialize_func=strategy_module.initialize, strategy_config=strategy_config
        )

        # 运行回测
        results = engine.run()

        # 处理结果数据，转换为可序列化的格式
        processed_results = process_backtest_results(results)

        # 更新结果到状态（同步更新，异步广播由包装器处理）
        backtest_state.results = processed_results

        logger.info(f'回测完成: {task_id}')
        return processed_results

    except Exception as e:
        logger.error(f'回测执行错误: {e}')
        raise e


def process_backtest_results(results: Dict[str, Any]) -> Dict[str, Any]:
    """处理回测结果，转换为前端可用的格式"""
    try:
        processed = {}

        # 处理投资组合历史数据
        if 'portfolio_history' in results:
            portfolio_df = results['portfolio_history']

            # 处理日期索引 - 检查索引类型
            if hasattr(portfolio_df.index, 'strftime'):
                # 如果是DatetimeIndex
                dates = portfolio_df.index.strftime('%Y-%m-%d').tolist()
            else:
                # 如果是其他类型的索引，尝试转换为字符串
                dates = [str(date) for date in portfolio_df.index]

            processed['portfolio_history'] = {
                'dates': dates,
                'total_value': portfolio_df['total_value'].tolist(),
                'available_cash': portfolio_df['available_cash'].tolist(),
                'returns': portfolio_df['returns'].fillna(0).tolist(),
                'cumulative_returns': (portfolio_df['returns'].fillna(0).cumsum()).tolist(),
            }

        # 处理性能统计
        if 'performance_stats' in results:
            processed['performance_stats'] = results['performance_stats']

        # 处理交易记录
        if 'trade_records' in results:
            trade_records = results['trade_records']
            processed['trade_records'] = []
            for record in trade_records:
                # 处理时间字段
                timestamp = record.get('timestamp')
                if timestamp and hasattr(timestamp, 'strftime'):
                    # 如果是datetime对象，格式化为日期字符串
                    date_str = timestamp.strftime('%Y-%m-%d')
                    datetime_str = timestamp.isoformat()
                elif timestamp and hasattr(timestamp, 'date'):
                    # 如果有date方法，提取日期部分
                    date_str = timestamp.date().strftime('%Y-%m-%d')
                    datetime_str = str(timestamp)
                elif timestamp:
                    # 尝试解析字符串格式的时间戳
                    try:
                        from datetime import datetime

                        if 'T' in str(timestamp):
                            # ISO格式时间戳
                            dt = datetime.fromisoformat(str(timestamp).replace('Z', '+00:00'))
                            date_str = dt.strftime('%Y-%m-%d')
                            datetime_str = str(timestamp)
                        else:
                            date_str = str(timestamp)
                            datetime_str = str(timestamp)
                    except:
                        date_str = str(timestamp)
                        datetime_str = str(timestamp)
                else:
                    date_str = ''
                    datetime_str = ''

                # 处理买卖方向
                side = record.get('side', '')
                action = '买入' if side == 'buy' else '卖出' if side == 'sell' else side

                # 计算交易金额
                volume = record.get('volume', 0)
                price = record.get('price', 0)
                amount = volume * price

                processed['trade_records'].append(
                    {
                        'date': date_str,  # 前端期望的字段名是date，格式为YYYY-MM-DD
                        'datetime': datetime_str,  # 保留datetime字段以兼容其他可能的用途
                        'symbol': record.get('symbol', ''),
                        'action': action,
                        'volume': volume,
                        'price': price,
                        'amount': amount,
                        'commission': record.get('commission', 0),
                        'tax': 0,  # 当前系统中税费单独计算，这里设为0
                    }
                )

        # 处理最终持仓
        if 'final_positions' in results:
            positions = results['final_positions']
            processed['final_positions'] = [
                {
                    'symbol': symbol,
                    'volume': pos.total_volume,
                    'available_volume': pos.closeable_volume,
                    'avg_cost': pos.avg_cost,
                    'market_value': pos.value,
                }
                for symbol, pos in positions.items()
                if pos.total_volume > 0
            ]

        return processed

    except Exception as e:
        logger.error(f'处理回测结果错误: {e}')
        return {'error': str(e)}


# WebSocket路由
@app.websocket('/ws')
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        # 发送当前状态
        await websocket.send_json({'type': 'status_update', 'data': backtest_state.to_dict()})

        # 保持连接活跃
        while True:
            # 等待客户端消息（心跳检测）
            try:
                data = await websocket.receive_text()
                # 可以处理客户端发送的消息，比如心跳包
                if data == 'ping':
                    await websocket.send_json({'type': 'pong'})
            except WebSocketDisconnect:
                logger.info('WebSocket客户端主动断开连接')
                break
            except Exception as e:
                logger.error(f'WebSocket接收消息错误: {e}')
                break
    except WebSocketDisconnect:
        logger.info('WebSocket连接断开')
    except Exception as e:
        logger.error(f'WebSocket错误: {e}')
    finally:
        # 确保连接被清理
        manager.disconnect(websocket)


# API路由定义
@app.get('/strategies', response_model=StrategiesResponse)
async def get_strategies():
    """获取策略列表"""
    try:
        strategies = get_strategy_files()
        return StrategiesResponse(strategies=[StrategyInfo(**strategy) for strategy in strategies])
    except Exception as e:
        logger.error(f'获取策略列表错误: {e}')
        raise HTTPException(status_code=500, detail=f'获取策略列表失败: {str(e)}')


@app.post('/backtest/start', response_model=BacktestResponse)
async def start_backtest(request: BacktestRequest, background_tasks: BackgroundTasks):
    """开始回测"""
    global backtest_state

    try:
        # 检查是否已有回测在运行
        if backtest_state.is_running:
            raise HTTPException(status_code=409, detail='已有回测任务在运行中')

        # 查找策略文件
        strategies = get_strategy_files()
        strategy_file = None
        for strategy in strategies:
            if strategy['name'] == request.strategy:
                strategy_file = strategy['path']
                break

        if not strategy_file:
            raise HTTPException(status_code=404, detail=f'未找到策略文件: {request.strategy}')

        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 更新状态
        backtest_state.current_strategy = request.strategy
        backtest_state.start_date = request.start_date
        backtest_state.end_date = request.end_date
        backtest_state.task_id = task_id
        backtest_state.results = None
        backtest_state.error_message = None

        # 在后台执行回测
        background_tasks.add_task(
            run_backtest_in_background, strategy_file, request.start_date, request.end_date, task_id
        )

        return BacktestResponse(message='回测已开始', task_id=task_id)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'启动回测错误: {e}')
        raise HTTPException(status_code=500, detail=f'启动回测失败: {str(e)}')


async def run_backtest_in_background(
    strategy_path: str, start_date: str, end_date: str, task_id: str
):
    """在后台运行回测"""
    loop = asyncio.get_event_loop()
    try:
        # 广播开始状态
        await backtest_state.update_status(
            is_running=True, progress=0, status='running', error_message=None
        )

        # 在线程池中执行回测
        await loop.run_in_executor(
            executor, run_backtest_sync, strategy_path, start_date, end_date, task_id
        )

        # 广播完成状态
        await backtest_state.update_status(is_running=False, progress=100, status='completed')

    except Exception as e:
        logger.error(f'后台回测任务执行失败: {e}')
        # 广播错误状态
        await backtest_state.update_status(is_running=False, status='error', error_message=str(e))


if __name__ == '__main__':
    # 设置日志
    setup_logging()

    print('=' * 60)
    print('QuantBack FastAPI 回测服务器')
    print('=' * 60)
    print(f'启动时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    print('=' * 60)

    # 启动服务器
    uvicorn.run(app, host='0.0.0.0', port=8000, log_level='info')
