import atexit
import logging

import clickhouse_connect

import quantback

logger = logging.getLogger(__name__)

# 全局ClickHouse客户端实例
_clickhouse_client = None
# 标记是否已注册atexit清理函数
_atexit_registered = False


def get_clickhouse_client():
    """
    获取ClickHouse客户端连接（复用连接）
    """
    global _clickhouse_client

    # 如果已有连接，直接返回
    # 移除健康检查以提升性能. 本身client有query_retries次数.
    if _clickhouse_client is not None:
        return _clickhouse_client

    # 创建新连接
    try:
        config = quantback.get_config()
        host = config.get('clickhouse.host')
        port = config.get('clickhouse.port')
        logger.info(f'正在连接ClickHouse数据库: {host}:{port}')
        _clickhouse_client = clickhouse_connect.get_client(
            host=host, port=port, username='default', password=''
        )
        logger.info('ClickHouse数据库连接成功')

        # 注册程序退出时的清理函数（只注册一次）
        global _atexit_registered
        if not _atexit_registered:
            atexit.register(_close_clickhouse_client)
            _atexit_registered = True

        return _clickhouse_client
    except Exception as e:
        logger.error(f'ClickHouse数据库连接失败: {str(e)}')
        raise


def create_database():
    """
    创建ClickHouse数据库和所需的表结构
    """
    try:
        logger.info('连接ClickHouse数据库.')

        # 创建ClickHouse连接
        client = get_clickhouse_client()

        logger.info('创建表结构...')

        # 创建日线数据表 (k_day)
        # 按月分区，支持高效的时间范围查询和数据管理
        create_k_day_sql = """
        CREATE TABLE IF NOT EXISTS k_day (
            stock_code String,
            trade_date Date,
            type String,
            open Float64,
            high Float64,
            low Float64,
            close Float64,
            pre_close Float64,
            change Float64 MATERIALIZED close-pre_close,
            pct_change Float64 MATERIALIZED (close-pre_close)/pre_close,
            volume Float64,
            amount Float64,
            high_limit Float64,
            low_limit Float64
        ) ENGINE = MergeTree()
        PARTITION BY toYear(trade_date)
        ORDER BY (stock_code,trade_date);

        ALTER TABLE k_day MODIFY COMMENT '日线表, 包含stock, index, etf';
        """

        client.command(create_k_day_sql)

        # 创建分钟线数据表 (k_1m)
        # 按日分区，优化分钟级数据的查询性能
        # ORDER BY 优先按时间排序，便于时间范围查询，然后按股票代码排序
        create_k_1m_sql = """
        CREATE TABLE IF NOT EXISTS k_1m (
            stock_code String,
            trade_time DateTime,
            open Float64,
            high Float64,
            low Float64,
            close Float64,
            pre_close Float64,
            change Float64 MATERIALIZED close-pre_close,
            pct_change Float64 MATERIALIZED (close-pre_close)/pre_close,
            volume Float64,
            amount Float64,
            high_limit Float64,
            low_limit Float64
        ) ENGINE = MergeTree()
        PARTITION BY toYYYYMM(trade_time)
        ORDER BY (stock_code,trade_time)
        """  # 也可考虑用天分区

        client.command(create_k_1m_sql)

        # 创建分红送转信息表 (dividend_info)
        # 按年分区，分红数据相对较少，按年分区即可
        create_dividend_sql = """
        CREATE TABLE IF NOT EXISTS dividend_info (
            stock_code String,
            ex_date Date,
            interest Float64 COMMENT '每股股利(税前，元)',
            stock_bonus Float64 COMMENT '每股红股(股)',
            stock_gift Float64 COMMENT '每股转增股本(股)',
            allot_num Float64 COMMENT '每股配股数(股)',
            allot_price Float64 COMMENT '配股价格(元)',
            gugai Float64 COMMENT '是否股改',
            dr Float64 COMMENT '除权系数'
        ) ENGINE = MergeTree()
        ORDER BY (stock_code, ex_date);

        ALTER TABLE dividend_info MODIFY COMMENT '分红送转表, 包含stock, etf';
        """

        client.command(create_dividend_sql)

        # 创建交易日历表 (trading_calendar)
        # 存储交易日期，用于判断是否为交易日
        create_trading_calendar_sql = """
        CREATE TABLE IF NOT EXISTS trading_calendar (
            trade_date Date COMMENT '交易日期'
        ) ENGINE = MergeTree()
        ORDER BY trade_date
        """

        client.command(create_trading_calendar_sql)

        # 创建股票基础快照表 (stock_basic_snapshot)
        # 存储历史A股每日基础信息快照
        create_stock_basic_snapshot_sql = """
        CREATE TABLE IF NOT EXISTS stock_basic_snapshot (
            trade_date Date COMMENT '交易日期',
            stock_code String COMMENT '股票代码',
            start_date Date COMMENT '上市日期',
            type String COMMENT '股票类型',
            is_st Bool COMMENT '是否ST股票',
            paused Bool COMMENT '是否停牌',
            name_at_time String COMMENT '当时股票名称'
        ) ENGINE = MergeTree()
        PARTITION BY toYear(trade_date)
        ORDER BY (trade_date, stock_code)
        """

        client.command(create_stock_basic_snapshot_sql)

        # 创建股票财务快照表 (stock_finance_snapshot)
        # 存储历史A股每日财务指标快照
        create_stock_finance_snapshot_sql = """
        CREATE TABLE IF NOT EXISTS stock_finance_snapshot (
            trade_date Date COMMENT '交易日期',
            stock_code String COMMENT '股票代码',
            capitalization Float64 COMMENT '总股本(万股)',
            circulating_cap Float64 COMMENT '流通股本(万股)',
            market_cap Float64 COMMENT '总市值(亿元)',
            circulating_market_cap Float64 COMMENT '流通市值(亿元)',
            pe_ratio Float64 COMMENT '动态市盈率',
            pb_ratio Float64 COMMENT '市净率',
            eps Float64 COMMENT '每股收益(元)',
            roe Float64 COMMENT '净资产收益率(%)',
            roa Float64 COMMENT '总资产收益率(%)'
        ) ENGINE = MergeTree()
        PARTITION BY toYear(trade_date)
        ORDER BY (trade_date, stock_code)
        """

        client.command(create_stock_finance_snapshot_sql)

        # 不关闭连接，复用连接

        logger.info('ClickHouse数据库表结构创建完成')
        return True

    except Exception as e:
        logger.error(f'创建ClickHouse数据库时出错: {str(e)}')
        return False


def _close_clickhouse_client():
    """
    关闭ClickHouse客户端连接（程序退出时调用）
    """
    global _clickhouse_client
    if _clickhouse_client is not None:
        try:
            _clickhouse_client.close()
            logger.info('ClickHouse数据库连接已关闭')
        except Exception as e:
            logger.warning(f'关闭ClickHouse连接时出错: {str(e)}')
        finally:
            _clickhouse_client = None
