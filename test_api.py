#!/usr/bin/env python3
"""
测试新的FastAPI后端服务器
"""

import requests
import time
import json

BASE_URL = "http://localhost:8000"

def test_health():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    response = requests.get(f"{BASE_URL}/health")
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    assert response.status_code == 200
    print("✅ 健康检查通过\n")

def test_strategies():
    """测试获取策略列表"""
    print("🔍 测试获取策略列表...")
    response = requests.get(f"{BASE_URL}/api/v1/strategies")
    print(f"状态码: {response.status_code}")
    data = response.json()
    print(f"策略数量: {len(data['strategies'])}")
    for strategy in data['strategies']:
        print(f"  - {strategy['name']} ({strategy['filename']})")
    assert response.status_code == 200
    assert len(data['strategies']) > 0
    print("✅ 策略列表获取成功\n")
    return data['strategies']

def test_backtest_status():
    """测试获取回测状态"""
    print("🔍 测试获取回测状态...")
    response = requests.get(f"{BASE_URL}/api/v1/backtest/status")
    print(f"状态码: {response.status_code}")
    data = response.json()
    print(f"回测状态: {data}")
    assert response.status_code == 200
    print("✅ 回测状态获取成功\n")
    return data

def test_start_backtest(strategies):
    """测试开始回测"""
    if not strategies:
        print("❌ 没有可用的策略，跳过回测测试")
        return
    
    strategy = strategies[0]
    print(f"🔍 测试开始回测 - 策略: {strategy['name']}...")
    
    backtest_data = {
        "strategy": strategy['name'],
        "start_date": "2024-01-01",
        "end_date": "2024-01-31"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/backtest/start", json=backtest_data)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"回测已开始: {data}")
        print("✅ 回测启动成功")
        
        # 监控回测状态
        print("🔍 监控回测状态...")
        for i in range(10):  # 最多监控10次
            time.sleep(2)
            status_response = requests.get(f"{BASE_URL}/api/v1/backtest/status")
            status_data = status_response.json()
            print(f"  第{i+1}次检查 - 状态: {status_data['status']}, 进度: {status_data['progress']}%")
            
            if status_data['status'] == 'completed':
                print("✅ 回测完成!")
                if status_data['results']:
                    print("✅ 回测结果获取成功")
                break
            elif status_data['status'] == 'error':
                print(f"❌ 回测出错: {status_data['error_message']}")
                break
        else:
            print("⏰ 回测仍在运行中...")
            
    else:
        print(f"❌ 回测启动失败: {response.text}")
    
    print()

def main():
    """主测试函数"""
    print("🚀 开始测试FastAPI后端服务器")
    print("=" * 50)
    
    try:
        # 测试基础API
        test_health()
        strategies = test_strategies()
        test_backtest_status()
        
        # 测试回测功能
        test_start_backtest(strategies)
        
        print("🎉 所有测试完成!")
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器已启动")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
