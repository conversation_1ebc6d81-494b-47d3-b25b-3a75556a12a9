import react from '@vitejs/plugin-react'
import { defineConfig } from 'vite'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  server: {
    watch: {
      //此处如果不ignore, 会造成vite首次启动加载很慢.
      //被发现变更的文件通过plugins判断是否需要hmr. /src路径下的.txt文件会触发整个页面刷新, 而其它目录的txt不会.
      ignored: [
        '**/quantback/**',
        '**/.venv/**',
        '**/.idea/**',
        '**/.vscode/**',
      ],
    },
  },
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
})
