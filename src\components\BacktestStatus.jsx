function BacktestStatus({ status }) {
  const { is_running, current_strategy, start_date, end_date, progress } = status

  return (
    <div className="card bg-base-100 shadow-xl">
      <div className="card-body">
        <h2 className="card-title">
          回测状态
          <div className={`badge ${is_running ? 'badge-warning' : 'badge-success'}`}>
            {is_running ? '运行中' : '已完成'}
          </div>
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="stat">
            <div className="stat-title">当前策略</div>
            <div className="stat-value text-lg">{current_strategy || '无'}</div>
          </div>

          <div className="stat">
            <div className="stat-title">回测期间</div>
            <div className="stat-value text-lg">
              {start_date && end_date ? `${start_date} 至 ${end_date}` : '未设置'}
            </div>
          </div>

          <div className="stat">
            <div className="stat-title">进度</div>
            <div className="stat-value text-lg">{progress}%</div>
          </div>
        </div>

        {is_running && (
          <div className="mt-4">
            <div className="flex justify-between text-sm mb-1">
              <span>回测进度</span>
              <span>{progress}%</span>
            </div>
            <progress 
              className="progress progress-primary w-full" 
              value={progress} 
              max="100"
            ></progress>
          </div>
        )}
      </div>
    </div>
  )
}

export default BacktestStatus
