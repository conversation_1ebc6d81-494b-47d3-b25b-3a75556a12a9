# QuantBack 量化回测 GUI 系统

一个完整的前后端量化回测系统，提供直观的 Web 界面进行策略回测和结果分析。

## 功能特性

### 🎯 核心功能

- **策略选择**: 从 `quantback/port_bt/strategies/` 目录自动加载策略文件
- **日期配置**: 灵活设置回测的起始和结束日期
- **实时监控**: 回测运行期间实时显示进度和状态
- **结果展示**: 完整的回测结果分析和可视化

### 📊 数据展示

- **收益曲线图**: 使用 Highcharts 展示投资组合价值和累积收益率
- **统计指标**: 总收益率、年化收益率、最大回撤、夏普比率等
- **每日市值**: 详细的每日投资组合价值变化
- **持仓明细**: 最终持仓的详细信息
- **成交记录**: 完整的交易历史记录

### 🎨 界面特性

- **响应式设计**: 支持桌面和移动设备
- **实时通信**: 基于 Socket.IO 的前后端实时通信
- **现代 UI**: 使用 Tailwind CSS 和 daisyUI 的现代化界面
- **数据过滤**: 支持按股票代码、操作类型等过滤数据

## 技术栈

### 前端

- **框架**: React 19 + JavaScript
- **构建工具**: Vite 7
- **样式**: Tailwind CSS v4 + daisyUI
- **图表**: Highcharts + highcharts-react-official
- **通信**: socket.io-client

### 后端

- **语言**: Python 3.11
- **框架**: python-socketio + uvicorn (ASGI)
- **回测引擎**: 基于现有的 quantback.port_bt 模块
- **并发**: asyncio + ThreadPoolExecutor

## 快速开始

### 1. 安装依赖

```bash
# 安装Python依赖
uv sync

# 安装Node.js依赖
npm install
```

### 2. 启动系统

#### 方式一：使用启动脚本（推荐）

```bash
python start_system.py
```

#### 方式二：分别启动

```bash
# 终端1：启动后端服务器
python quantback/backtest_server.py

# 终端2：启动前端开发服务器
npm run dev
```

### 3. 访问系统

- **前端界面**: http://localhost:5173
- **后端 API**: http://localhost:8001

## 使用指南

### 1. 策略文件准备

确保策略文件位于 `quantback/port_bt/strategies/` 目录下，策略文件需要包含：

- `initialize` 函数：策略初始化
- `STRATEGY_CONFIG` 字典：策略配置

### 2. 运行回测

1. 在前端界面选择策略文件
2. 设置回测的开始和结束日期
3. 点击"开始回测"按钮
4. 实时查看回测进度和状态

### 3. 查看结果

回测完成后，可以查看：

- **概览**: 收益曲线图和统计指标
- **每日市值**: 详细的每日投资组合数据
- **持仓明细**: 最终持仓情况
- **成交记录**: 完整的交易历史

## 项目结构

```
├── quantback/
│   ├── backtest_server.py          # Socket.IO后端服务器
│   └── port_bt/
│       ├── strategies/             # 策略文件目录
│       └── ...                     # 回测引擎模块
├── src/
│   ├── App.jsx                     # 主应用组件
│   ├── components/
│   │   ├── BacktestDashboard.jsx   # 回测仪表板
│   │   ├── StrategySelector.jsx    # 策略选择器
│   │   ├── DateSelector.jsx        # 日期选择器
│   │   ├── BacktestControls.jsx    # 回测控制
│   │   ├── BacktestStatus.jsx      # 回测状态
│   │   ├── ReturnsChart.jsx        # 收益曲线图
│   │   ├── ResultsDisplay.jsx      # 结果展示
│   │   ├── PerformanceStats.jsx    # 性能统计
│   │   ├── PortfolioTable.jsx      # 投资组合表格
│   │   ├── PositionsTable.jsx      # 持仓表格
│   │   └── TradeRecords.jsx        # 成交记录表格
│   └── index.css                   # 样式文件
├── start_system.py                 # 系统启动脚本
└── README_BACKTEST_GUI.md          # 本文档
```

## Socket.IO 事件

### 客户端发送

- `get_strategies`: 获取策略列表
- `start_backtest`: 开始回测
- `get_backtest_status`: 获取回测状态

### 服务器发送

- `welcome`: 连接欢迎消息
- `strategies_list`: 策略列表响应
- `backtest_started`: 回测开始通知
- `backtest_status`: 回测状态更新
- `backtest_completed`: 回测完成通知
- `backtest_error`: 回测错误通知
- `error`: 一般错误消息

## 注意事项

1. **单用户使用**: 当前版本设计为本地单用户使用，无认证机制
2. **回测并发**: 同时只能运行一个回测任务
3. **数据源**: 依赖现有的 ClickHouse 数据源
4. **策略格式**: 策略文件需要符合 quantback.port_bt 的格式要求

## 故障排除

### 常见问题

1. **连接失败**

   - 检查后端服务器是否正常启动
   - 确认端口 8000 和 5173 未被占用

2. **策略加载失败**

   - 检查策略文件格式是否正确
   - 确认策略文件包含必要的函数和配置

3. **回测执行错误**
   - 查看后端终端输出的详细错误信息
   - 检查数据源连接是否正常

### 日志查看

- 后端日志：在运行 `python quantback/backtest_server.py` 的终端查看
- 前端日志：在浏览器开发者工具的 Console 中查看

## 扩展开发

系统采用模块化设计，可以轻松扩展：

1. **添加新的图表类型**: 在 `src/components/` 中创建新的图表组件
2. **扩展统计指标**: 修改 `PerformanceStats.jsx` 组件
3. **添加新的过滤器**: 在相应的表格组件中添加过滤逻辑
4. **自定义主题**: 修改 `src/index.css` 和 Tailwind 配置

## 许可证

本项目遵循与主项目相同的许可证。
