import { useEffect, useRef } from 'react'
import Highcharts from 'highcharts'
import HighchartsReact from 'highcharts-react-official'

function ReturnsChart({ portfolioHistory }) {
  const chartRef = useRef(null)

  if (!portfolioHistory || !portfolioHistory.dates || portfolioHistory.dates.length === 0) {
    return (
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h2 className="card-title">收益曲线</h2>
          <div className="flex items-center justify-center h-64">
            <p className="text-gray-500">暂无数据</p>
          </div>
        </div>
      </div>
    )
  }

  // 准备图表数据
  const dates = portfolioHistory.dates.map(date => new Date(date).getTime())
  const totalValueData = portfolioHistory.total_value || []
  const cumulativeReturnsData = portfolioHistory.cumulative_returns || []

  // 计算累积收益率百分比
  const cumulativeReturnsPercent = cumulativeReturnsData.map(value => value * 100)

  const chartOptions = {
    chart: {
      type: 'line',
      height: 400,
      backgroundColor: 'transparent',
      style: {
        fontFamily: 'inherit'
      }
    },
    title: {
      text: '投资组合收益曲线',
      style: {
        fontSize: '18px',
        fontWeight: 'bold'
      }
    },
    xAxis: {
      type: 'datetime',
      title: {
        text: '日期'
      },
      gridLineWidth: 1,
      gridLineColor: '#e5e7eb'
    },
    yAxis: [
      {
        title: {
          text: '总市值 (元)',
          style: {
            color: '#3b82f6'
          }
        },
        labels: {
          style: {
            color: '#3b82f6'
          },
          formatter: function() {
            return Highcharts.numberFormat(this.value, 0, '.', ',')
          }
        },
        gridLineColor: '#e5e7eb'
      },
      {
        title: {
          text: '累积收益率 (%)',
          style: {
            color: '#10b981'
          }
        },
        labels: {
          style: {
            color: '#10b981'
          },
          formatter: function() {
            return this.value.toFixed(2) + '%'
          }
        },
        opposite: true
      }
    ],
    series: [
      {
        name: '总市值',
        data: dates.map((date, index) => [date, totalValueData[index]]),
        color: '#3b82f6',
        yAxis: 0,
        tooltip: {
          valuePrefix: '¥',
          valueDecimals: 2
        }
      },
      {
        name: '累积收益率',
        data: dates.map((date, index) => [date, cumulativeReturnsPercent[index]]),
        color: '#10b981',
        yAxis: 1,
        tooltip: {
          valueSuffix: '%',
          valueDecimals: 2
        }
      }
    ],
    tooltip: {
      shared: true,
      crosshairs: true,
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e5e7eb',
      borderRadius: 8,
      shadow: true
    },
    legend: {
      enabled: true,
      align: 'center',
      verticalAlign: 'bottom',
      layout: 'horizontal'
    },
    plotOptions: {
      line: {
        lineWidth: 2,
        marker: {
          enabled: false,
          states: {
            hover: {
              enabled: true,
              radius: 5
            }
          }
        }
      }
    },
    credits: {
      enabled: false
    },
    responsive: {
      rules: [{
        condition: {
          maxWidth: 500
        },
        chartOptions: {
          legend: {
            layout: 'horizontal',
            align: 'center',
            verticalAlign: 'bottom'
          }
        }
      }]
    }
  }

  return (
    <div className="card bg-base-100 shadow-xl">
      <div className="card-body">
        <HighchartsReact
          highcharts={Highcharts}
          options={chartOptions}
          ref={chartRef}
        />
      </div>
    </div>
  )
}

export default ReturnsChart
